[gd_resource type="BehaviorTree" load_steps=12 format=3]

[ext_resource type="Script" path="res://addons/limboai/resources/behavior_tree.gd" id="1_patrol"]
[ext_resource type="Script" path="res://addons/limboai/nodes/composites/selector.gd" id="2_patrol"]
[ext_resource type="Script" path="res://addons/limboai/nodes/composites/sequence.gd" id="3_patrol"]
[ext_resource type="Script" path="res://addons/limboai/nodes/actions/bt_wait.gd" id="4_patrol"]
[ext_resource type="Script" path="res://Project/AI/tasks/PatrolTask.gd" id="5_patrol"]
[ext_resource type="Script" path="res://Project/AI/tasks/FindTargetTask.gd" id="6_patrol"]
[ext_resource type="Script" path="res://Project/AI/tasks/MoveToTargetTask.gd" id="7_patrol"]

[sub_resource type="BTSelector" id="BTSelector_patrol"]
script = ExtResource("2_patrol")

[sub_resource type="BTSequence" id="BTSequence_patrol"]
script = ExtResource("3_patrol")

[sub_resource type="BTSequence" id="BTSequence_investigate"]
script = ExtResource("3_patrol")

[sub_resource type="BTPatrolTask" id="BTPatrolTask_patrol"]
script = ExtResource("5_patrol")
patrol_points_var = &"patrol_points"
current_point_var = &"current_patrol_point"
speed_multiplier = 0.8
wait_time = 3.0
point_reached_distance = 1.5

[sub_resource type="BTFindTargetTask" id="BTFindTargetTask_patrol"]
script = ExtResource("6_patrol")
target_var = &"patrol_target"
detection_radius = 8.0

[sub_resource type="BTMoveToTargetTask" id="BTMoveToTargetTask_patrol"]
script = ExtResource("7_patrol")
target_var = &"patrol_target"
position_var = &"target_position"
speed_multiplier = 1.0
arrival_distance = 2.0

[sub_resource type="BTWait" id="BTWait_patrol"]
wait_time = 1.0
script = ExtResource("4_patrol")

[resource]
root_task = SubResource("BTSelector_patrol")
blackboard_plan = {
"patrol_points": [],
"current_patrol_point": 0,
"patrol_target": null,
"target_position": Vector3(0, 0, 0),
"is_patrolling": true,
"last_patrol_time": 0.0
}
script = ExtResource("1_patrol")

[node name="PatrolSelector" type="Node" parent="."]
script = ExtResource("2_patrol")

[node name="InvestigateSequence" type="Node" parent="PatrolSelector"]
script = ExtResource("3_patrol")

[node name="FindTarget" type="Node" parent="PatrolSelector/InvestigateSequence"]
script = ExtResource("6_patrol")
target_var = &"patrol_target"
detection_radius = 8.0

[node name="MoveToTarget" type="Node" parent="PatrolSelector/InvestigateSequence"]
script = ExtResource("7_patrol")
target_var = &"patrol_target"
position_var = &"target_position"
speed_multiplier = 1.0
arrival_distance = 2.0

[node name="PatrolSequence" type="Node" parent="PatrolSelector"]
script = ExtResource("3_patrol")

[node name="PatrolPoints" type="Node" parent="PatrolSelector/PatrolSequence"]
script = ExtResource("5_patrol")
patrol_points_var = &"patrol_points"
current_point_var = &"current_patrol_point"
speed_multiplier = 0.8
wait_time = 3.0
point_reached_distance = 1.5

[node name="Wait" type="Node" parent="PatrolSelector"]
script = ExtResource("4_patrol")
wait_time = 1.0
