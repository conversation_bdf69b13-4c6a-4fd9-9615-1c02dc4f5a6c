extends BTAction
class_name MoveToTargetTask

@export var target_var: StringName = &"target"
@export var position_var: StringName = &"target_position"
@export var speed_multiplier: float = 1.0
@export var arrival_distance: float = 1.5
@export var update_rotation: bool = true

func _tick(delta: float) -> Status:
    var target = blackboard.get_var(target_var)
    var target_position: Vector3
    
    if target and is_instance_valid(target):
        target_position = target.global_position
        blackboard.set_var(position_var, target_position)
    else:
        target_position = blackboard.get_var(position_var, Vector3.ZERO)
        if target_position == Vector3.ZERO:
            return FAILURE
    
    # Calculate direction and distance
    var direction = target_position - agent.global_position
    direction.y = 0  # Ignore height difference
    var distance = direction.length()
    
    # Check if we've arrived
    if distance <= arrival_distance:
        return SUCCESS
    
    # Move towards target
    direction = direction.normalized()
    
    # Get movement component
    var movement_component = agent.movement_component
    if movement_component:
        # Set movement vector
        movement_component.set_movement_vector(Vector2(direction.x, direction.z))
        
        # Set appropriate movement type based on distance
        if distance > 5.0:
            agent.state_component.change_state("running")
        else:
            agent.state_component.change_state("walking")
    else:
        # Fallback movement if no component
        agent.velocity = direction * speed_multiplier * 5.0
        agent.move_and_slide()
    
    # Update rotation if needed
    if update_rotation:
        var target_rotation = atan2(direction.x, direction.z)
        agent.rotation.y = lerp_angle(agent.rotation.y, target_rotation, 5.0 * delta)
    
    return RUNNING