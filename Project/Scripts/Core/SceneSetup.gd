
extends Node
class_name SceneSetup

@export var spawn_player: bool = true
@export var player_spawn_point: NodePath
@export var enemy_spawn_points: Array[NodePath] = []
@export var npc_spawn_points: Array[NodePath] = []

@export_group("Enemy Types")
@export var enemy_types: Array[String] = []

@export_group("NPC Types")
@export var npc_types: Array[String] = []

@export_group("Camera")
@export var camera_target_path: NodePath
@export var use_phantom_camera: bool = true

var player: Entity

func _ready():
	var game_manager = GameManager
	if not game_manager:
		push_error("GameManager not found in SceneSetup")
		return
	
	# Spawn player if needed
	if spawn_player:
		var spawn_position = Vector3.ZERO
		
		if not player_spawn_point.is_empty():
			var spawn_node = get_node(player_spawn_point)
			if spawn_node:
				spawn_position = spawn_node.global_position
		
		player = game_manager.entity_manager.create_player(spawn_position)
		add_child(player)
		
		# Setup camera
		if use_phantom_camera and has_node("PhantomCamera"):
			var phantom_camera = get_node("PhantomCamera")
			if phantom_camera.has_method("set_follow_target"):
				phantom_camera.set_follow_target(player)
		elif not camera_target_path.is_empty():
			var camera = get_node(camera_target_path)
			if camera and camera is Camera3D:
				# Simple follow logic could be implemented here
				pass
	
	# Spawn enemies
	for i in range(min(enemy_spawn_points.size(), enemy_types.size())):
		var spawn_node = get_node(enemy_spawn_points[i])
		if spawn_node:
			var enemy = game_manager.entity_manager.create_enemy(
				enemy_types[i], 
				spawn_node.global_position
			)
			add_child(enemy)
	
	# Spawn NPCs
	for i in range(min(npc_spawn_points.size(), npc_types.size())):
		var spawn_node = get_node(npc_spawn_points[i])
		if spawn_node:
			var npc = game_manager.entity_manager.create_npc(
				npc_types[i], 
				spawn_node.global_position
			)
			add_child(npc)
