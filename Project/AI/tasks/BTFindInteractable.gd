extends BTAction
class_name BTFindInteractable

# This task finds the nearest interactable object and stores it in the blackboard

@export var target_var: StringName = "current_interactable"
@export var can_interact_var: StringName = "can_interact"
@export var interaction_component_path: NodePath
@export var max_search_distance: float = 5.0
@export var search_angle: float = 60.0  # Degrees

var interaction_component: InteractionComponent

func _setup():
    # Get the interaction component
    if not interaction_component_path.is_empty():
        interaction_component = agent.get_node(interaction_component_path)
    else:
        # Try to find the interaction component in the agent
        interaction_component = agent.get_node_or_null("InteractionComponent")
    
    if not interaction_component:
        push_error("BTFindInteractable: No InteractionComponent found in agent")
        return FAILURE

func _tick(_delta: float):
    # Find interactables in the scene
    var interactables = agent.get_tree().get_nodes_in_group("Interactable")
    if interactables.size() == 0:
        blackboard.set_var(can_interact_var, false)
        return FAILURE
    
    # Find the nearest interactable
    var nearest = null
    var nearest_distance = max_search_distance
    
    for interactable in interactables:
        if not is_instance_valid(interactable):
            continue
        
        # Check if in range
        var distance = agent.global_position.distance_to(interactable.global_position)
        if distance > max_search_distance:
            continue
        
        # Check if in view angle
        var forward = -agent.global_transform.basis.z
        var direction = (interactable.global_position - agent.global_position).normalized()
        var angle_dot = forward.dot(direction)
        var angle_deg = rad_to_deg(acos(clamp(angle_dot, -1.0, 1.0)))
        
        if angle_deg > search_angle:
            continue
        
        # Check if closer than current nearest
        if nearest == null or distance < nearest_distance:
            nearest = interactable
            nearest_distance = distance
    
    # Store the result in the blackboard
    blackboard.set_var(target_var, nearest)
    blackboard.set_var(can_interact_var, is_instance_valid(nearest))
    
    return SUCCESS if is_instance_valid(nearest) else FAILURE
