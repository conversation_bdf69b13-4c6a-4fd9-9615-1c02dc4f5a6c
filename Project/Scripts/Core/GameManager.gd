extends Node

# Core systems
var entity_manager
var ai_manager
var class_manager
var scene_manager

# Game state
var game_paused: bool = false
var player_entity: Entity = null
var current_scene_name: String = ""

func _ready():
	# Get reference to SceneManager autoload
	scene_manager = get_node("/root/SceneManager")
	
	# Initialize core systems
	entity_manager = EntityManager.new()
	add_child(entity_manager)
	
	ai_manager = AIManager.new()
	add_child(ai_manager)
	
	class_manager = ClassManager.new()
	add_child(class_manager)
	
	# Connect signals
	entity_manager.connect("player_spawned", _on_player_spawned)
	
	print("GameManager initialized")

func _on_player_spawned(player: Entity):
	player_entity = player
	print("Player entity registered with GameManager")
	
	# Assign default class to player if needed
	if not class_manager.get_entity_class(player):
		# Default to Spectral class
		class_manager.assign_class(player, "Spectral", {
			"spectral_type": SpectralClass.SpectralType.GHOST
		})

func pause_game():
	game_paused = true
	get_tree().paused = true
	
func resume_game():
	game_paused = false
	get_tree().paused = false

func change_scene(scene_name: String):
	if scene_manager:
		current_scene_name = scene_name
		scene_manager.change_scene_to_node(scene_name)

# Class system helpers
func assign_player_class(class_id: String, params: Dictionary = {}):
	if player_entity:
		return class_manager.assign_class(player_entity, class_id, params)
	return null

func get_player_class():
	if player_entity:
		return class_manager.get_entity_class(player_entity)
	return null

func activate_player_class_ability(ability_name: String):
	if not player_entity:
		return false
		
	match ability_name:
		"phase":
			return class_manager.activate_phasing(player_entity)
		"rage":
			return class_manager.activate_rage_mode(player_entity)
		"meditate":
			return class_manager.start_meditation(player_entity)
		"end_meditation":
			return class_manager.end_meditation(player_entity)
	
	return false
