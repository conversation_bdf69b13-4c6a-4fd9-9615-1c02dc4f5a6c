extends Node
class_name BehaviorTreeManager

# Registry of behavior trees
var behavior_tree_registry: Dictionary = {}

# Entities using behavior trees
var registered_entities: Dictionary = {}

# Configuration
var enabled: bool = true
var debug_mode: bool = false

func _ready():
	# Load and register default behavior trees
	_load_default_behavior_trees()

func _load_default_behavior_trees():
	# Combat behavior tree
	var combat_bt = load("res://Project/AI/Trees/CombatBT.gd").new()
	register_behavior_tree("combat", combat_bt)
	
	# Patrol behavior tree
	var patrol_bt = load("res://Project/AI/Trees/PatrolBT.gd").new()
	register_behavior_tree("patrol", patrol_bt)
	
	# Investigate behavior tree
	var investigate_bt = load("res://Project/AI/Trees/InvestigateBT.gd").new()
	register_behavior_tree("investigate", investigate_bt)

func register_behavior_tree(name: String, behavior_tree):
	behavior_tree_registry[name] = behavior_tree
	print("Registered behavior tree: " + name)

func register_entity(entity: Entity, ai_component):
	if not registered_entities.has(entity.entity_id):
		registered_entities[entity.entity_id] = {
			"entity": entity,
			"ai_component": ai_component,
			"active_tree": null
		}
		
		# Set up the behavior tree for this entity
		var tree_name = ai_component.behavior_tree_name
		if behavior_tree_registry.has(tree_name):
			var tree_instance = behavior_tree_registry[tree_name].duplicate()
			registered_entities[entity.entity_id]["active_tree"] = tree_instance
			ai_component.set_behavior_tree(tree_instance)

func unregister_entity(entity: Entity):
	if registered_entities.has(entity.entity_id):
		registered_entities.erase(entity.entity_id)

func set_enabled(value: bool):
	enabled = value
	for entity_data in registered_entities.values():
		entity_data["ai_component"].set_enabled(enabled)

func set_debug_mode(value: bool):
	debug_mode = value
	for entity_data in registered_entities.values():
		entity_data["ai_component"].set_debug_mode(debug_mode)
