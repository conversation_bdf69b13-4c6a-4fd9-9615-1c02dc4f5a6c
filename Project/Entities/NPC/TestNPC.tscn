[gd_scene load_steps=6 format=3 uid="uid://bqxnpvs5yvhxl"]

[ext_resource type="Script" path="res://Project/Entities/NPC/TestNPC.gd" id="1_ck3ys"]
[ext_resource type="Resource" uid="uid://dqxnpvs5yvhxl" path="res://Project/AI/TestBehaviorTree.tres" id="2_lfmxs"]

[sub_resource type="CapsuleMesh" id="CapsuleMesh_ixnqr"]

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_ixnqr"]
albedo_color = Color(0.8, 0.2, 0.2, 1)

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_ixnqr"]
height = 2.0

[node name="TestNPC" type="CharacterBody3D"]
script = ExtResource("1_ck3ys")
behavior_tree = ExtResource("2_lfmxs")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
mesh = SubResource("CapsuleMesh_ixnqr")
surface_material_override/0 = SubResource("StandardMaterial3D_ixnqr")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 0)
shape = SubResource("CapsuleShape3D_ixnqr")

[node name="NavigationAgent3D" type="NavigationAgent3D" parent="."]

[node name="BTPlayer" type="BTPlayer" parent="."]
behavior_tree = ExtResource("2_lfmxs")