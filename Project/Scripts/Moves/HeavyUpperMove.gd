extends MoveStrategy
class_name HeavyUpperMove

func _init():
    damage = 18.0
    stamina_cost = 15.0
    cooldown = 0.8
    animation_name = "heavy_upper_attack"
    hit_frame = 12

var charge_time: float = 0.0
var max_charge_time: float = 1.0
var charge_damage_multiplier: float = 1.5
var is_charging: bool = false
var charge_complete: bool = false

func execute():
    # Reset charge state
    charge_time = 0.0
    is_charging = true
    charge_complete = false
    
    # Don't use stamina yet - will be used on release
    
    # Play charge animation
    component.entity.model_component.play_animation("heavy_upper_charge")
    
    # Change entity state
    component.entity.change_state("attacking")

func update(delta: float):
    if is_charging:
        # Update charge time
        charge_time += delta
        
        # Check if charge is complete
        if charge_time >= max_charge_time and not charge_complete:
            charge_complete = true
            component.entity.model_component.play_animation("heavy_upper_charged")
        
        # Check for release input (handled by combat component)
        # The combat component should call release_charge() when attack button is released
    else:
        # Normal attack update after release
        super.update(delta)

func release_charge():
    is_charging = false
    
    # Calculate damage multiplier based on charge time
    var charge_factor = min(charge_time / max_charge_time, 1.0)
    damage = damage * (1.0 + charge_factor * (charge_damage_multiplier - 1.0))
    
    # Use stamina now
    component.entity.resource_component.use_stamina(stamina_cost)
    
    # Play attack animation
    component.entity.model_component.play_animation(animation_name)
    
    # Reset current time for normal attack sequence
    current_time = 0.0
    has_hit = false

func _find_targets_in_range() -> Array:
    var targets = []
    
    # Get forward direction
    var forward = -component.entity.global_transform.basis.z.normalized()
    
    # Define attack range and angle
    var attack_range = 2.2
    var attack_angle = deg_to_rad(60.0)
    
    # Get potential targets (enemies in the "enemy" group)
    var potential_targets = component.entity.get_node("BTPlayer").agent.get_nodes_in_group("enemy")
    if component.entity.is_in_group("enemy"):
        # If we're an enemy, target the player instead
        potential_targets = component.entity.get_node("BTPlayer").agent.get_nodes_in_group("player")
    
    # Check each potential target
    for target in potential_targets:
        # Skip if target is self
        if target == component.entity:
            continue
        
        # Check if target is in range
        var to_target = target.global_position - component.entity.global_position
        to_target.y = 0  # Ignore height difference
        var distance = to_target.length()
        
        if distance <= attack_range:
            # Check if target is within attack angle
            to_target = to_target.normalized()
            var angle = acos(clamp(forward.dot(to_target), -1.0, 1.0))
            
            if angle <= attack_angle / 2:
                # Target is in range and within angle
                targets.append(target)
    
    return targets

func apply_damage():
    # Find targets in range
    var targets = _find_targets_in_range()
    
    for target in targets:
        if target.has_method("take_hit"):
            var damage_info = {
                "damage": component.calculate_damage(damage),
                "source": component.entity,
                "type": "physical",
                "can_be_blocked": true,
                "attack_type": "heavy_upper",
                "knockback": true,
                "knockback_force": 5.0,
                "stagger": true
            }
            target.take_hit(damage_info)

func can_be_interrupted() -> bool:
    # Can be interrupted during charge, but not during attack
    return is_charging