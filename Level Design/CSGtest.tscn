[gd_scene load_steps=2 format=3 uid="uid://cbnygp84ifqht"]

[ext_resource type="PackedScene" uid="uid://cqa15r4k1equd" path="res://WIP files/Player/Player_entity.tscn" id="1_1r8ls"]

[node name="CsGtest" type="Node3D"]

[node name="CSGBox3D" type="CSGBox3D" parent="."]
transform = Transform3D(2.19303, 0, 0, 0, 2.72044, 0, 0, 0, 1, 0, 0.708407, 0)

[node name="CSGCylinder3D" type="CSGCylinder3D" parent="CSGBox3D"]
transform = Transform3D(0.972559, 0, 0, 0, -0.0128894, 0.783905, 0, -2.13256, -0.0350647, 0, -0.0930464, -0.00748595)
operation = 2
sides = 16

[node name="CSGBox3D2" type="CSGBox3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5.77223, 4.76837e-07, 0.0165968)
use_collision = true
flip_faces = true
size = Vector3(4.89746, 3, 19.332)

[node name="CSGBox3D3" type="CSGBox3D" parent="CSGBox3D2"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 0.18605, 3.64073, 0, 0)
flip_faces = true
size = Vector3(4.89746, 3, 19.332)

[node name="Player" parent="." instance=ExtResource("1_1r8ls")]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, -1, 1)
