extends BTAction
class_name FindTargetTask

@export var target_group: StringName = &"Player"
@export var target_var: StringName = &"target"
@export var detection_radius_var: StringName = &"detection_radius"
@export var store_position: bool = true
@export var position_var: StringName = &"target_position"

func _tick(_delta: float) -> Status:
    var detection_radius = blackboard.get_var(detection_radius_var, 10.0)
    
    # Find all potential targets
    var potential_targets = agent.get_nodes_in_group(target_group)
    var closest_target = null
    var closest_distance = detection_radius
    
    for target in potential_targets:
        var distance = agent.global_position.distance_to(target.global_position)
        
        if distance < closest_distance:
            # Check line of sight
            var space_state = agent.get_world_3d().direct_space_state
            var query = PhysicsRayQueryParameters3D.create(
                agent.global_position + Vector3(0, 1, 0),  # Start from eye level
                target.global_position + Vector3(0, 1, 0)
            )
            query.exclude = [agent]
            var result = space_state.intersect_ray(query)
            
            if result.is_empty() or result.collider == target:
                closest_target = target
                closest_distance = distance
    
    # Store the target in blackboard
    blackboard.set_var(target_var, closest_target)
    
    # Store position if requested
    if store_position and closest_target:
        blackboard.set_var(position_var, closest_target.global_position)
    
    return SUCCESS if closest_target else FAILURE