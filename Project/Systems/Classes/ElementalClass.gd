extends BaseClass
class_name ElementalClass

enum ElementType {FIRE, WATER, EARTH, AIR}

@export var element_type: ElementType = ElementType.FIRE
@export var element_damage_bonus: float = 0.2
@export var element_resistance: float = 0.3

func _init():
    name = "Elemental"
    class_description = "Masters of elemental magic"
    
    # Elemental class has higher mana but lower health
    health_modifier = 0.9
    mana_modifier = 1.5
    stamina_modifier = 0.8
    attack_modifier = 1.1
    defense_modifier = 0.9
    speed_modifier = 1.0
    
    # Set up passive effects based on element type
    _setup_element_effects()

func _setup_element_effects():
    match element_type:
        ElementType.FIRE:
            passive_effects["fire_damage"] = element_damage_bonus
            passive_effects["ice_resistance"] = -element_resistance
        ElementType.WATER:
            passive_effects["water_damage"] = element_damage_bonus
            passive_effects["fire_resistance"] = element_resistance
        ElementType.EARTH:
            passive_effects["earth_damage"] = element_damage_bonus
            passive_effects["air_resistance"] = element_resistance
        ElementType.AIR:
            passive_effects["air_damage"] = element_damage_bonus
            passive_effects["earth_resistance"] = -element_resistance

func apply_passive_effects(entity):
    var combat_component = entity.get_node_or_null("CombatComponent")
    if combat_component:
        # Apply elemental damage bonuses to projectiles
        combat_component.connect("projectile_created", _on_projectile_created)

func _on_projectile_created(projectile):
    # Add elemental effects to projectiles
    var element_name = ElementType.keys()[element_type].to_lower()
    projectile.set_element(element_name)
    projectile.set_damage_bonus(element_damage_bonus)
    
    # Add visual effects based on element
    var effect_scene = load("res://Project/Effects/" + element_name + "_effect.tscn")
    if effect_scene:
        var effect = effect_scene.instantiate()
        projectile.add_child(effect)