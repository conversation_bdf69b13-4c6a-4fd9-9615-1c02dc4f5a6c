[gd_scene load_steps=10 format=3 uid="uid://c8l60rnugru6f"]

[ext_resource type="Script" uid="uid://dh86t0o1u6awh" path="res://WIP files/Player/PlayerCompatibility.gd" id="1_compat"]
[ext_resource type="PackedScene" uid="uid://dkl1i7wju7vba" path="res://WIP files/Player/Visuals/PlayerVisuals.tscn" id="2_yjnpd"]
[ext_resource type="Script" uid="uid://7wn5q54kirr" path="res://Project/Scripts/Components/MovementComponent.gd" id="3_mvcomp"]
[ext_resource type="Script" uid="uid://4ct6mnw2mk3j" path="res://Project/Scripts/Components/StateComponent.gd" id="4_stcomp"]
[ext_resource type="Script" uid="uid://bqxt7fyrrynjk" path="res://Project/Scripts/Components/CombatComponent.gd" id="5_cbcomp"]
[ext_resource type="Script" uid="uid://bc7tilajrk0l4" path="res://Project/Scripts/Components/ResourceComponent.gd" id="6_rscomp"]
[ext_resource type="Script" uid="uid://bnt6n8f4r8x2u" path="res://Project/Scripts/Components/ModelComponent.gd" id="7_mdcomp"]
[ext_resource type="Script" uid="uid://c1t4lx3ntller" path="res://Project/Scripts/Controllers/PlayerController.gd" id="8_plcont"]

[sub_resource type="CapsuleShape3D" id="CapsuleShape3D_q8r5d"]
radius = 0.35
height = 1.8

[node name="Player" type="CharacterBody3D" groups=["Player"]]
collision_layer = 2
script = ExtResource("1_compat")

[node name="Visuals" parent="." instance=ExtResource("2_yjnpd")]

[node name="Beta_Joints" parent="Visuals" index="0"]
material_override = null

[node name="Beta_Surface" parent="Visuals" index="1"]
material_override = null

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.9, 0)
shape = SubResource("CapsuleShape3D_q8r5d")

[node name="MovementComponent" type="Node" parent="."]
script = ExtResource("3_mvcomp")
jump_height = 2.0
rotation_speed = 8.0
available_movements = Array[String](["idle", "walking", "running", "sprinting", "jumping"])

[node name="StateComponent" type="Node" parent="."]
script = ExtResource("4_stcomp")
available_states = Array[String](["grounded", "in_air", "attacking", "defending", "aiming", "crouching", "prone", "stealth"])

[node name="CombatComponent" type="Node" parent="."]
script = ExtResource("5_cbcomp")

[node name="ResourceComponent" type="Node" parent="."]
script = ExtResource("6_rscomp")

[node name="ModelComponent" type="Node" parent="."]
script = ExtResource("7_mdcomp")

[node name="PlayerController" type="Node" parent="."]
script = ExtResource("8_plcont")

[editable path="Visuals"]
