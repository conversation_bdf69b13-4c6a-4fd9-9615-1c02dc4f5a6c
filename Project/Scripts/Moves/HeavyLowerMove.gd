extends MoveStrategy
class_name HeavyLowerMove

func _init():
    damage = 16.0
    stamina_cost = 12.0
    cooldown = 0.7
    animation_name = "heavy_lower_attack"
    hit_frame = 10

var sweep_angle: float = deg_to_rad(180.0)  # Wide sweep attack
var sweep_range: float = 2.0

func _find_targets_in_range() -> Array:
    var agent = component.entity.get_node("BTPlayer").agent
    var targets = []
    
    # Get forward direction
    var forward = -component.entity.global_transform.basis.z.normalized()
    var right = component.entity.global_transform.basis.x.normalized()
    
    # Get potential targets (enemies in the "enemy" group)
    var potential_targets = agent.get_nodes_in_group("enemy")
    if component.entity.is_in_group("enemy"):
        # If we're an enemy, target the player instead
        potential_targets = agent.get_nodes_in_group("player")
    
    # Check each potential target
    for target in potential_targets:
        # Skip if target is self
        if target == component.entity:
            continue
        
        # Check if target is in range
        var to_target = target.global_position - component.entity.global_position
        to_target.y = 0  # Ignore height difference
        var distance = to_target.length()
        
        if distance <= sweep_range:
            # Check if target is within sweep angle
            to_target = to_target.normalized()
            var angle = acos(clamp(forward.dot(to_target), -1.0, 1.0))
            
            # For sweep attacks, we need to check if the target is in front
            var side = right.dot(to_target)
            var is_in_front = forward.dot(to_target) > 0
            
            if angle <= sweep_angle / 2 and is_in_front:
                # Target is in range and within angle
                targets.append(target)
    
    return targets

func apply_damage():
    # Find targets in range
    var targets = _find_targets_in_range()
    
    for target in targets:
        if target.has_method("take_hit"):
            var damage_info = {
                "damage": component.calculate_damage(damage),
                "source": component.entity,
                "type": "physical",
                "can_be_blocked": true,
                "attack_type": "heavy_lower",
                "knockback": true,
                "knockback_force": 3.0,
                "trip": true  # Can trip/knock down enemies
            }
            target.take_hit(damage_info)

func update(delta: float):
    super.update(delta)
    
    # Add a slight forward movement during the attack
    if current_time >= hit_frame / 60.0 - 0.1 and current_time <= hit_frame / 60.0 + 0.1:
        var forward = -component.entity.global_transform.basis.z.normalized()
        component.entity.velocity = forward * 3.0