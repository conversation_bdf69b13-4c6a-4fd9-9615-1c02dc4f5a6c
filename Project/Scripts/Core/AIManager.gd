extends Node
class_name AIManager

# Managers for different AI systems
var behavior_tree_manager: BehaviorTreeManager
var state_machine_manager: StateMachineManager

# AI configuration
var ai_enabled: bool = true
var debug_mode: bool = false

func _ready():
	behavior_tree_manager = BehaviorTreeManager.new()
	add_child(behavior_tree_manager)
	
	state_machine_manager = StateMachineManager.new()
	add_child(state_machine_manager)
	
	print("AIManager initialized")

func register_ai_entity(entity: Entity):
	# Check if entity has an AI component
	var ai_component = entity.components.get("AIComponent")
	if ai_component:
		if ai_component.ai_type == "behavior_tree":
			behavior_tree_manager.register_entity(entity, ai_component)
		elif ai_component.ai_type == "state_machine":
			state_machine_manager.register_entity(entity, ai_component)

func unregister_ai_entity(entity: Entity):
	behavior_tree_manager.unregister_entity(entity)
	state_machine_manager.unregister_entity(entity)

func set_ai_enabled(enabled: bool):
	ai_enabled = enabled
	behavior_tree_manager.set_enabled(enabled)
	state_machine_manager.set_enabled(enabled)

func set_debug_mode(enabled: bool):
	debug_mode = enabled
	behavior_tree_manager.set_debug_mode(enabled)
	state_machine_manager.set_debug_mode(enabled)
