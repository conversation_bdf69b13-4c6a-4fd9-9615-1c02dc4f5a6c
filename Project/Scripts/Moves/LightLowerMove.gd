extends MoveStrategy
class_name LightLowerMove

func _init():
    damage = 8.0
    stamina_cost = 6.0
    cooldown = 0.4
    animation_name = "light_lower_attack"
    hit_frame = 6

func _find_targets_in_range() -> Array:
    var targets = []
    
    # Get forward direction
    var forward = -component.entity.global_transform.basis.z.normalized()
    
    # Define attack range and angle
    var attack_range = 1.8
    var attack_angle = deg_to_rad(70.0)
    
    # Get potential targets (enemies in the "enemy" group)
    var potential_targets = component.entity.get_node("BTPlayer").agent.get_nodes_in_group("enemy")
    if component.entity.is_in_group("enemy"):
        # If we're an enemy, target the player instead
        potential_targets = component.entity.get_node("BTPlayer").agent.get_nodes_in_group("player")
    
    # Check each potential target
    for target in potential_targets:
        # Skip if target is self
        if target == component.entity:
            continue
        
        # Check if target is in range
        var to_target = target.global_position - component.entity.global_position
        to_target.y = 0  # Ignore height difference
        var distance = to_target.length()
        
        if distance <= attack_range:
            # Check if target is within attack angle
            to_target = to_target.normalized()
            var angle = acos(clamp(forward.dot(to_target), -1.0, 1.0))
            
            if angle <= attack_angle / 2:
                # Target is in range and within angle
                targets.append(target)
    
    return targets

func apply_damage():
    # Find targets in range
    var targets = _find_targets_in_range()
    
    for target in targets:
        if target.has_method("take_hit"):
            var damage_info = {
                "damage": component.calculate_damage(damage),
                "source": component.entity,
                "type": "physical",
                "can_be_blocked": true,
                "attack_type": "light_lower",
                "knockback": true,
                "knockback_force": 2.0
            }
            target.take_hit(damage_info)