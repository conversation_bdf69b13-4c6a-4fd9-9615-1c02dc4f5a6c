extends BaseClass
class_name SpectralClass

enum SpectralType {GHOST, PHANTOM, WRAITH, SPIRIT}

@export var spectral_type: SpectralType = SpectralType.GHOST
@export var phase_duration: float = 5.0
@export var phase_cooldown: float = 15.0
@export var invisibility_strength: float = 0.7

var is_phasing: bool = false
var phase_timer: float = 0.0
var cooldown_timer: float = 0.0

func _init():
    name = "Spectral"
    class_description = "Masters of the ethereal realm"
    
    # Spectral class has higher speed but lower health
    health_modifier = 0.8
    mana_modifier = 1.2
    stamina_modifier = 1.1
    attack_modifier = 0.9
    defense_modifier = 0.7
    speed_modifier = 1.3
    
    # Set up passive effects based on spectral type
    _setup_spectral_effects()

func _setup_spectral_effects():
    match spectral_type:
        SpectralType.GHOST:
            passive_effects["invisibility"] = 0.3
            passive_effects["phase_cooldown_reduction"] = 0.2
        SpectralType.PHANTOM:
            passive_effects["fear_aura"] = 0.4
            passive_effects["phase_duration_bonus"] = 0.3
        SpectralType.WRAITH:
            passive_effects["life_drain"] = 0.2
            passive_effects["damage_bonus_to_living"] = 0.3
        SpectralType.SPIRIT:
            passive_effects["mana_regen"] = 0.4
            passive_effects["spectral_resistance"] = 0.5

func apply_passive_effects(entity):
    # Connect to process for phasing ability
    entity.connect("process", _on_entity_process)
    
    # Apply visual effects based on spectral type
    var model_component = entity.get_node_or_null("ModelComponent")
    if model_component and model_component.model_instance:
        var material_override = ShaderMaterial.new()
        var shader = load("res://Project/Shaders/spectral_shader.gdshader")
        if shader:
            material_override.shader = shader
            material_override.set_shader_parameter("invisibility", passive_effects.get("invisibility", 0.0))
            
            # Apply to all meshes
            _apply_material_to_meshes(model_component.model_instance, material_override)

func _apply_material_to_meshes(node, material):
    if node is MeshInstance3D:
        node.material_override = material
    
    for child in node.get_children():
        _apply_material_to_meshes(child, material)

func _on_entity_process(entity, delta):
    # Handle phasing ability
    if is_phasing:
        phase_timer += delta
        if phase_timer >= phase_duration:
            _end_phasing(entity)
    elif cooldown_timer > 0:
        cooldown_timer -= delta

func activate_phasing(entity):
    if cooldown_timer <= 0 and not is_phasing:
        is_phasing = true
        phase_timer = 0.0
        
        # Apply phasing effects
        entity.set_collision_layer_value(1, false)  # Disable collision with environment
        
        # Visual effect
        var model_component = entity.get_node_or_null("ModelComponent")
        if model_component and model_component.model_instance:
            var material_override = model_component.model_instance.get_surface_override_material(0)
            if material_override and material_override is ShaderMaterial:
                material_override.set_shader_parameter("invisibility", invisibility_strength)

func _end_phasing(entity):
    is_phasing = false
    phase_timer = 0.0
    cooldown_timer = phase_cooldown
    
    # Remove phasing effects
    entity.set_collision_layer_value(1, true)  # Re-enable collision with environment
    
    # Reset visual effect
    var model_component = entity.get_node_or_null("ModelComponent")
    if model_component and model_component.model_instance:
        var material_override = model_component.model_instance.get_surface_override_material(0)
        if material_override and material_override is ShaderMaterial:
            material_override.set_shader_parameter("invisibility", passive_effects.get("invisibility", 0.0))