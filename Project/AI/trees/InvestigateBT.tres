[gd_resource type="BehaviorTree" load_steps=13 format=3]

[ext_resource type="Script" path="res://addons/limboai/resources/behavior_tree.gd" id="1_investigate"]
[ext_resource type="Script" path="res://addons/limboai/nodes/composites/selector.gd" id="2_investigate"]
[ext_resource type="Script" path="res://addons/limboai/nodes/composites/sequence.gd" id="3_investigate"]
[ext_resource type="Script" path="res://addons/limboai/nodes/actions/bt_wait.gd" id="4_investigate"]
[ext_resource type="Script" path="res://Project/AI/tasks/FindTargetTask.gd" id="5_investigate"]
[ext_resource type="Script" path="res://Project/AI/tasks/MoveToTargetTask.gd" id="6_investigate"]
[ext_resource type="Script" path="res://Project/AI/tasks/BTFindInteractable.gd" id="7_investigate"]
[ext_resource type="Script" path="res://Project/AI/tasks/BTInteractWithTarget.gd" id="8_investigate"]

[sub_resource type="BTSelector" id="BTSelector_investigate"]
script = ExtResource("2_investigate")

[sub_resource type="BTSequence" id="BTSequence_investigate"]
script = ExtResource("3_investigate")

[sub_resource type="BTSequence" id="BTSequence_search"]
script = ExtResource("3_investigate")

[sub_resource type="BTFindTargetTask" id="BTFindTargetTask_investigate"]
script = ExtResource("5_investigate")
target_var = &"investigation_target"
detection_radius = 12.0

[sub_resource type="BTMoveToTargetTask" id="BTMoveToTargetTask_investigate"]
script = ExtResource("6_investigate")
target_var = &"investigation_target"
position_var = &"investigation_position"
speed_multiplier = 0.9
arrival_distance = 1.5

[sub_resource type="BTFindInteractable" id="BTFindInteractable_investigate"]
script = ExtResource("7_investigate")
target_var = &"interactable_target"
can_interact_var = &"can_interact"
max_search_distance = 5.0
search_angle = 90.0

[sub_resource type="BTInteractWithTarget" id="BTInteractWithTarget_investigate"]
script = ExtResource("8_investigate")
target_var = &"interactable_target"
can_interact_var = &"can_interact"

[sub_resource type="BTWait" id="BTWait_investigate"]
wait_time = 2.0
script = ExtResource("4_investigate")

[resource]
root_task = SubResource("BTSelector_investigate")
blackboard_plan = {
"investigation_target": null,
"investigation_position": Vector3(0, 0, 0),
"interactable_target": null,
"can_interact": false,
"is_investigating": false,
"investigation_time": 0.0,
"search_radius": 5.0
}
script = ExtResource("1_investigate")

[node name="InvestigateSelector" type="Node" parent="."]
script = ExtResource("2_investigate")

[node name="InvestigateSequence" type="Node" parent="InvestigateSelector"]
script = ExtResource("3_investigate")

[node name="FindTarget" type="Node" parent="InvestigateSelector/InvestigateSequence"]
script = ExtResource("5_investigate")
target_var = &"investigation_target"
detection_radius = 12.0

[node name="MoveToTarget" type="Node" parent="InvestigateSelector/InvestigateSequence"]
script = ExtResource("6_investigate")
target_var = &"investigation_target"
position_var = &"investigation_position"
speed_multiplier = 0.9
arrival_distance = 1.5

[node name="SearchSequence" type="Node" parent="InvestigateSelector"]
script = ExtResource("3_investigate")

[node name="FindInteractable" type="Node" parent="InvestigateSelector/SearchSequence"]
script = ExtResource("7_investigate")
target_var = &"interactable_target"
can_interact_var = &"can_interact"
max_search_distance = 5.0
search_angle = 90.0

[node name="InteractWithTarget" type="Node" parent="InvestigateSelector/SearchSequence"]
script = ExtResource("8_investigate")
target_var = &"interactable_target"
can_interact_var = &"can_interact"

[node name="Wait" type="Node" parent="InvestigateSelector"]
script = ExtResource("4_investigate")
wait_time = 2.0
