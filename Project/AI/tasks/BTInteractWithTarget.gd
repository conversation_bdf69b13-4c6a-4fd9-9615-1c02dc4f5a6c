extends BTAction
class_name BTInter<PERSON><PERSON><PERSON><PERSON>arget

# This task makes the agent interact with a target object

@export var target_var: StringName = "current_interactable"
@export var can_interact_var: StringName = "can_interact"
@export var interaction_component_path: NodePath

var interaction_component: InteractionComponent

func _setup():
    # Get the interaction component
    if not interaction_component_path.is_empty():
        interaction_component = agent.get_node(interaction_component_path)
    else:
        # Try to find the interaction component in the agent
        interaction_component = agent.get_node_or_null("InteractionComponent")
    
    if not interaction_component:
        push_error("BTInteractWithTarget: No InteractionComponent found in agent")
        return FAILURE

func _tick(_delta: float):
    # Check if we can interact
    if not blackboard.get_var(can_interact_var, false):
        return FAILURE
    
    # Get the target
    var target = blackboard.get_var(target_var)
    if not is_instance_valid(target):
        return FAILURE
    
    # Interact with the target
    if interaction_component:
        interaction_component.interact()
        return SUCCESS
    
    return FAILURE