extends BaseClass
class_name EtherealClass

enum EtherealType {MYST<PERSON>, DREAMER, ASTRAL, VOID}

@export var ethereal_type: EtherealType = EtherealType.MYSTIC
@export var mana_efficiency: float = 0.7  # Reduces mana cost by 30%
@export var spell_amplification: float = 0.25  # Increases spell damage by 25%
@export var meditation_rate: float = 0.1  # Mana regen rate during meditation

var is_meditating: bool = false
var meditation_timer: float = 0.0
var entity_ref: WeakRef

func _init():
    name = "Ethereal"
    class_description = "Masters of the mind and spirit"
    
    # Ethereal class has higher mana and speed but lower health and defense
    health_modifier = 0.7
    mana_modifier = 1.8
    stamina_modifier = 0.8
    attack_modifier = 0.7
    defense_modifier = 0.6
    speed_modifier = 1.2
    
    # Set up passive effects based on ethereal type
    _setup_ethereal_effects()

func _setup_ethereal_effects():
    match ethereal_type:
        EtherealType.MYSTIC:
            passive_effects["spell_penetration"] = 0.3
            passive_effects["mana_regen"] = 0.2
        EtherealType.DREAMER:
            passive_effects["illusion_strength"] = 0.4
            passive_effects["mental_resistance"] = 0.3
        EtherealType.ASTRAL:
            passive_effects["teleport_range"] = 0.5
            passive_effects["astral_sight"] = 0.4
        EtherealType.VOID:
            passive_effects["void_damage"] = 0.3
            passive_effects["mana_steal"] = 0.2

func apply_passive_effects(entity):
    # Store weak reference to entity
    entity_ref = weakref(entity)
    
    # Connect to process for meditation
    entity.connect("process", _on_entity_process)
    
    # Apply visual effects based on ethereal type
    var model_component = entity.get_node_or_null("ModelComponent")
    if model_component and model_component.model_instance:
        # Add ethereal glow effect
        var material_override = ShaderMaterial.new()
        var shader = load("res://Project/Shaders/ethereal_shader.gdshader")
        if shader:
            material_override.shader = shader
            
            # Set shader parameters based on ethereal type
            match ethereal_type:
                EtherealType.MYSTIC:
                    material_override.set_shader_parameter("glow_color", Color(0.5, 0.2, 0.8, 0.7))
                EtherealType.DREAMER:
                    material_override.set_shader_parameter("glow_color", Color(0.2, 0.6, 0.8, 0.7))
                EtherealType.ASTRAL:
                    material_override.set_shader_parameter("glow_color", Color(0.8, 0.8, 0.2, 0.7))
                EtherealType.VOID:
                    material_override.set_shader_parameter("glow_color", Color(0.1, 0.1, 0.3, 0.8))
            
            # Apply to all meshes
            _apply_material_to_meshes(model_component.model_instance, material_override)

func _apply_material_to_meshes(node, material):
    if node is MeshInstance3D:
        node.material_override = material
    
    for child in node.get_children():
        _apply_material_to_meshes(child, material)

func _on_entity_process(entity, delta):
    # Handle meditation
    if is_meditating:
        meditation_timer += delta
        
        # Regenerate mana during meditation
        var resource_component = entity.get_node_or_null("ResourceComponent")
        if resource_component:
            resource_component.mana += resource_component.max_mana * meditation_rate * delta

func start_meditation(entity):
    if not is_meditating:
        is_meditating = true
        meditation_timer = 0.0
        
        # Apply meditation effects
        var movement_component = entity.get_node_or_null("MovementComponent")
        if movement_component:
            movement_component.set_movement_type("idle")
            movement_component.lock_movement = true
        
        # Visual effects
        var model_component = entity.get_node_or_null("ModelComponent")
        if model_component:
            # Play meditation animation
            if model_component.animator:
                model_component.animator.play("meditation")
            
            # Add meditation particle effect
            var meditation_effect = load("res://Project/Effects/meditation_effect.tscn")
            if meditation_effect:
                var effect_instance = meditation_effect.instantiate()
                model_component.add_child(effect_instance)
                effect_instance.name = "MeditationEffect"

func end_meditation(entity):
    if is_meditating:
        is_meditating = false
        
        # Remove meditation effects
        var movement_component = entity.get_node_or_null("MovementComponent")
        if movement_component:
            movement_component.lock_movement = false
        
        # Remove visual effects
        var model_component = entity.get_node_or_null("ModelComponent")
        if model_component:
            var meditation_effect = model_component.get_node_or_null("MeditationEffect")
            if meditation_effect:
                meditation_effect.queue_free()

func modify_spell_cost(original_cost: float) -> float:
    return original_cost * mana_efficiency

func modify_spell_damage(original_damage: float) -> float:
    return original_damage * (1.0 + spell_amplification)