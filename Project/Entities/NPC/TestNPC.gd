extends CharacterBody3D

@export var behavior_tree: BehaviorTree
@export var move_speed: float = 3.0
@export var detection_radius: float = 8.0

@onready var nav_agent = $NavigationAgent3D
@onready var bt_player = $BTPlayer
var blackboard

func _ready():
    # Set up BTPlayer
    if behavior_tree:
        bt_player.behavior_tree = behavior_tree

    # Get blackboard reference
    blackboard = bt_player.blackboard

    # Initialize blackboard variables
    blackboard.set_var("guard_position", global_position)
    blackboard.set_var("detection_radius", detection_radius)
    blackboard.set_var("move_speed", move_speed)
    blackboard.set_var("agent", self)

func _physics_process(_delta):
    # Update player position in blackboard if player is detected
    var player = _find_player()
    if player and global_position.distance_to(player.global_position) < detection_radius:
        blackboard.set_var("player", player)
        blackboard.set_var("player_position", player.global_position)
    else:
        blackboard.set_var("player", null)

    # Update navigation if we have a target
    var target_position = blackboard.get_var("target_position")
    if target_position:
        nav_agent.target_position = target_position

        var next_position = nav_agent.get_next_path_position()
        var direction = (next_position - global_position).normalized()

        # Apply movement
        velocity = direction * move_speed
        move_and_slide()
    else:
        velocity = Vector3.ZERO

func _find_player():
    # Find player in the scene
    var players = get_tree().get_nodes_in_group("Player")
    if players.size() > 0:
        return players[0]
    return null