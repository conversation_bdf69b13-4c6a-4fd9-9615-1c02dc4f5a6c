extends Resource
class_name BaseClass

@export var name: String = "Base"
@export var class_description: String = "A basic character class"
@export var class_icon: Texture2D

# Base stats modifiers
@export var health_modifier: float = 1.0
@export var mana_modifier: float = 1.0
@export var stamina_modifier: float = 1.0
@export var attack_modifier: float = 1.0
@export var defense_modifier: float = 1.0
@export var speed_modifier: float = 1.0

# Available abilities for this class
@export var abilities: Array[Resource] = []

# Class-specific passive effects
var passive_effects: Dictionary = {}

func apply_class_modifiers(resource_component):
    if resource_component:
        resource_component.max_health *= health_modifier
        resource_component.health = resource_component.max_health
        
        resource_component.max_mana *= mana_modifier
        resource_component.mana = resource_component.max_mana
        
        resource_component.max_stamina *= stamina_modifier
        resource_component.stamina = resource_component.max_stamina

func apply_combat_modifiers(combat_component):
    if combat_component:
        combat_component.base_damage *= attack_modifier
        combat_component.base_defense *= defense_modifier

func apply_movement_modifiers(movement_component):
    if movement_component:
        movement_component.walk_speed *= speed_modifier
        movement_component.run_speed *= speed_modifier
        movement_component.sprint_speed *= speed_modifier

func get_class_abilities():
    return abilities

func apply_passive_effects(entity):
    # Override in subclasses to apply class-specific passive effects
    pass