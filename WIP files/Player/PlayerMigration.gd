extends Node
class_name PlayerMigration

# This is a utility script to help migrate from the old Player system
# to the new component-based Entity system

static func migrate_player_to_entity(old_player_scene: PackedScene) -> PackedScene:
	# Instance the old player scene
	var old_player = old_player_scene.instantiate()
	
	# Create a new entity
	var new_entity = PlayerEntity.new()
	new_entity.name = old_player.name if old_player.name != "" else "PlayerEntity"
	
	# Copy transform and other basic properties
	new_entity.transform = old_player.transform
	new_entity.visible = old_player.visible
	new_entity.collision_layer = old_player.collision_layer
	new_entity.collision_mask = old_player.collision_mask
	
	# Add required components
	var movement_component = MovementComponent.new()
	movement_component.name = "MovementComponent"
	new_entity.add_child(movement_component)
	movement_component.owner = new_entity
	
	var state_component = StateComponent.new()
	state_component.name = "StateComponent"
	new_entity.add_child(state_component)
	state_component.owner = new_entity
	
	var combat_component = CombatComponent.new()
	combat_component.name = "CombatComponent"
	new_entity.add_child(combat_component)
	combat_component.owner = new_entity
	
	var resource_component = ResourceComponent.new()
	resource_component.name = "ResourceComponent"
	new_entity.add_child(resource_component)
	resource_component.owner = new_entity
	
	var model_component = ModelComponent.new()
	model_component.name = "ModelComponent"
	new_entity.add_child(model_component)
	model_component.owner = new_entity
	
	# Transfer visual elements
	_transfer_visual_elements(old_player, model_component, new_entity)
	
	# Transfer collision shapes
	_transfer_collision_shapes(old_player, new_entity)
	
	# Add interaction components
	_add_interaction_components(old_player, new_entity)
	
	# Add player controller
	var controller = PlayerController.new()
	controller.name = "PlayerController"
	new_entity.add_child(controller)
	controller.owner = new_entity
	
	# Configure components based on old player properties
	_configure_movement_component(movement_component, old_player)
	_configure_state_component(state_component)
	_configure_combat_component(combat_component, old_player)
	_configure_resource_component(resource_component, old_player)
	
	# Transfer any animations
	_transfer_animations(old_player, new_entity)
	
	# Transfer any scripts and properties
	_transfer_scripts_and_properties(old_player, new_entity)
	
	# Create a new scene
	var new_scene = PackedScene.new()
	var result = new_scene.pack(new_entity)
	if result != OK:
		print("Error packing scene: ", result)
		return null
	
	# Free the instances
	old_player.queue_free()
	
	return new_scene

static func _transfer_visual_elements(old_player, model_component, new_entity):
	# Look for common visual nodes
	var visual_node_names = ["MeshRoot", "Mesh", "Model", "Visuals", "Sprite3D", "MeshInstance3D"]
	
	for node_name in visual_node_names:
		if old_player.has_node(node_name):
			var visual_node = old_player.get_node(node_name).duplicate()
			model_component.add_child(visual_node)
			visual_node.owner = new_entity
			print("  Transferred visual node: ", node_name)
			return
	
	# If no common nodes found, look for any MeshInstance3D in the hierarchy
	for child in old_player.get_children():
		if child is MeshInstance3D:
			var mesh_instance = child.duplicate()
			model_component.add_child(mesh_instance)
			mesh_instance.owner = new_entity
			print("  Transferred mesh instance: ", child.name)
			return
		
		# Look one level deeper
		for grandchild in child.get_children():
			if grandchild is MeshInstance3D:
				var mesh_instance = grandchild.duplicate()
				model_component.add_child(mesh_instance)
				mesh_instance.owner = new_entity
				print("  Transferred nested mesh instance: ", grandchild.name)
				return
	
	print("  Warning: No visual elements found to transfer")

static func _transfer_collision_shapes(old_player, new_entity):
	# Look for collision shapes
	var collision_node_names = ["CollisionShape3D", "CollisionShape", "Collision"]
	
	for node_name in collision_node_names:
		if old_player.has_node(node_name):
			var collision_node = old_player.get_node(node_name).duplicate()
			new_entity.add_child(collision_node)
			collision_node.owner = new_entity
			print("  Transferred collision node: ", node_name)
			return
	
	# If no common nodes found, look for any CollisionShape3D in the hierarchy
	for child in old_player.get_children():
		if child is CollisionShape3D:
			var collision_shape = child.duplicate()
			new_entity.add_child(collision_shape)
			collision_shape.owner = new_entity
			print("  Transferred collision shape: ", child.name)
			return
	
	print("  Warning: No collision shapes found to transfer")

static func _transfer_animations(old_player, new_entity):
	# Look for animation players
	if old_player.has_node("AnimationPlayer"):
		var anim_player = old_player.get_node("AnimationPlayer").duplicate()
		new_entity.add_child(anim_player)
		anim_player.owner = new_entity
		print("  Transferred animation player")
	
	# Look for animation trees
	if old_player.has_node("AnimationTree"):
		var anim_tree = old_player.get_node("AnimationTree").duplicate()
		new_entity.add_child(anim_tree)
		anim_tree.owner = new_entity
		print("  Transferred animation tree")

static func _transfer_scripts_and_properties(old_player, new_entity):
	# Transfer custom properties (metadata)
	for meta_name in old_player.get_meta_list():
		var meta_value = old_player.get_meta(meta_name)
		new_entity.set_meta(meta_name, meta_value)
		print("  Transferred metadata: ", meta_name)

static func _configure_movement_component(component: MovementComponent, old_player):
	# Set up movement properties based on old player
	component.walk_speed = 3.0
	component.run_speed = 5.0
	component.sprint_speed = 8.0
	component.jump_height = 2.0
	
	# Add available movements
	component.available_movements = ["idle", "walking", "running", "sprinting", "jumping"]
	
	# If old player has movement controller, copy values
	if old_player.has_node("MovementController"):
		var old_controller = old_player.get_node("MovementController")
		if old_controller.get("rotation_speed") != null:
			component.rotation_speed = old_controller.rotation_speed
		if old_controller.get("fall_gravity") != null:
			component.gravity = old_controller.fall_gravity
		if old_controller.get("walk_speed") != null:
			component.walk_speed = old_controller.walk_speed
		if old_controller.get("run_speed") != null:
			component.run_speed = old_controller.run_speed
		if old_controller.get("sprint_speed") != null:
			component.sprint_speed = old_controller.sprint_speed
		if old_controller.get("jump_height") != null:
			component.jump_height = old_controller.jump_height
		print("  Configured movement component from old controller")

static func _configure_state_component(component: StateComponent):
	# Set up available states
	component.available_states = ["grounded", "in_air", "attacking", "defending", "aiming"]
	component.default_state = "grounded"
	print("  Configured state component with default states")

static func _configure_combat_component(component: CombatComponent, old_player):
	# Set up combat properties
	component.base_damage = 10.0
	component.base_defense = 5.0
	
	# Add available moves
	component.available_moves = {
		"light_upper": true,
		"heavy_upper": true,
		"light_lower": true,
		"heavy_lower": true,
		"dodge_roll": true
	}
	
	# If old player has active skill controller, copy values
	if old_player.has_node("ActiveSkillController"):
		var old_controller = old_player.get_node("ActiveSkillController")
		if old_controller.get("base_damage") != null:
			component.base_damage = old_controller.base_damage
		if old_controller.get("base_defense") != null:
			component.base_defense = old_controller.base_defense
		print("  Configured combat component from old controller")

static func _configure_resource_component(component: ResourceComponent, old_player):
	# Set up resource properties
	component.max_health = 100.0
	component.health = 100.0
	component.max_stamina = 100.0
	component.stamina = 100.0
	component.max_mana = 100.0
	component.mana = 100.0
	
	# If old player has a resource, copy values
	if old_player.has_meta("character_resource"):
		var old_resource = old_player.get_meta("character_resource")
		if old_resource:
			if old_resource.get("max_health") != null:
				component.max_health = old_resource.max_health
			if old_resource.get("health_count") != null:
				component.health = old_resource.health_count
			if old_resource.get("max_stamina") != null:
				component.max_stamina = old_resource.max_stamina
			if old_resource.get("stamina_count") != null:
				component.stamina = old_resource.stamina_count
			if old_resource.get("max_mana") != null:
				component.max_mana = old_resource.max_mana
			if old_resource.get("mana_count") != null:
				component.mana = old_resource.mana_count
			print("  Configured resource component from old resource")

static func _add_interaction_components(old_player, new_entity):
	# Add interaction component
	var interaction_component = load("res://Project/Scripts/Components/InteractionComponent.gd").new()
	interaction_component.name = "InteractionComponent"
	new_entity.add_child(interaction_component)
	interaction_component.owner = new_entity
	
	# Add interaction detector
	var interaction_detector = load("res://Project/Scripts/Components/InteractionDetector.gd").new()
	interaction_detector.name = "InteractionDetector"
	new_entity.add_child(interaction_detector)
	interaction_detector.owner = new_entity
	
	# Set up collision shape for detector
	var collision_shape = CollisionShape3D.new()
	collision_shape.name = "CollisionShape3D"
	var sphere_shape = SphereShape3D.new()
	sphere_shape.radius = 3.0  # Default interaction radius
	collision_shape.shape = sphere_shape
	interaction_detector.add_child(collision_shape)
	collision_shape.owner = new_entity
	
	# Configure interaction component
	interaction_component.interaction_distance = 3.0
	interaction_component.interaction_angle = 45.0
	
	# Look for existing interactor in old player
	if old_player.has_node("Interactor"):
		var old_interactor = old_player.get_node("Interactor")
		# Copy any relevant properties
		print("  Configured interaction component from old interactor")
	
	print("  Added interaction components")
