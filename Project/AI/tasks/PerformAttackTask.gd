extends BTAction
class_name PerformAttackTask

@export var target_var: StringName = &"target"
@export var attack_type_var: StringName = &"attack_type"
@export var default_attack: String = "light_upper"
@export var wait_for_completion: bool = true

var attack_started: bool = false

func _tick(_delta: float) -> Status:
    var target = blackboard.get_var(target_var)
    
    # Check if we have a valid target
    if not target or not is_instance_valid(target):
        return FAILURE
    
    # Get the combat component
    var combat_component = agent.combat_component
    if not combat_component:
        return FAILURE
    
    # If attack is already in progress
    if attack_started:
        if not wait_for_completion or not combat_component.is_attacking:
            attack_started = false
            return SUCCESS
        return RUNNING
    
    # Get attack type
    var attack_type = blackboard.get_var(attack_type_var, default_attack)
    
    # Face the target
    var direction = target.global_position - agent.global_position
    direction.y = 0
    if direction.length_squared() > 0.01:
        var target_rotation = atan2(direction.x, direction.z)
        agent.rotation.y = target_rotation
    
    # Perform the attack
    if combat_component.perform_move(attack_type):
        attack_started = true
        return RUNNING
    
    return FAILURE