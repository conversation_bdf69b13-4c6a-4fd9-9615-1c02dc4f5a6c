[gd_resource type="BehaviorTree" load_steps=15 format=3]

[ext_resource type="Script" path="res://addons/limboai/resources/behavior_tree.gd" id="1_combat"]
[ext_resource type="Script" path="res://addons/limboai/nodes/composites/selector.gd" id="2_combat"]
[ext_resource type="Script" path="res://addons/limboai/nodes/composites/sequence.gd" id="3_combat"]
[ext_resource type="Script" path="res://addons/limboai/nodes/decorators/inverter.gd" id="4_combat"]
[ext_resource type="Script" path="res://addons/limboai/nodes/actions/bt_wait.gd" id="5_combat"]
[ext_resource type="Script" path="res://Project/AI/tasks/FindTargetTask.gd" id="6_combat"]
[ext_resource type="Script" path="res://Project/AI/tasks/PursueTask.gd" id="7_combat"]
[ext_resource type="Script" path="res://Project/AI/tasks/PerformAttackTask.gd" id="8_combat"]
[ext_resource type="Script" path="res://Project/AI/tasks/FleeTask.gd" id="9_combat"]

[sub_resource type="BTSelector" id="BTSelector_combat"]
script = ExtResource("2_combat")

[sub_resource type="BTSequence" id="BTSequence_attack"]
script = ExtResource("3_combat")

[sub_resource type="BTSequence" id="BTSequence_flee"]
script = ExtResource("3_combat")

[sub_resource type="BTFindTargetTask" id="BTFindTargetTask_combat"]
script = ExtResource("6_combat")
target_var = &"combat_target"
detection_radius = 10.0

[sub_resource type="BTPursueTask" id="BTPursueTask_combat"]
script = ExtResource("7_combat")
target_var = &"combat_target"
speed_multiplier = 1.2
min_distance = 2.0

[sub_resource type="BTPerformAttackTask" id="BTPerformAttackTask_combat"]
script = ExtResource("8_combat")
target_var = &"combat_target"
default_attack = "light_upper"
wait_for_completion = true

[sub_resource type="BTFleeTask" id="BTFleeTask_combat"]
script = ExtResource("9_combat")
target_var = &"combat_target"
speed_multiplier = 1.5
safe_distance = 15.0

[sub_resource type="BTWait" id="BTWait_combat"]
wait_time = 0.5
script = ExtResource("5_combat")

[resource]
root_task = SubResource("BTSelector_combat")
blackboard_plan = {
"combat_target": null,
"health_percentage": 1.0,
"is_in_combat": false,
"last_attack_time": 0.0
}
script = ExtResource("1_combat")

[node name="CombatSelector" type="Node" parent="."]
script = ExtResource("2_combat")

[node name="AttackSequence" type="Node" parent="CombatSelector"]
script = ExtResource("3_combat")

[node name="FindTarget" type="Node" parent="CombatSelector/AttackSequence"]
script = ExtResource("6_combat")
target_var = &"combat_target"
detection_radius = 10.0

[node name="PursueTarget" type="Node" parent="CombatSelector/AttackSequence"]
script = ExtResource("7_combat")
target_var = &"combat_target"
speed_multiplier = 1.2
min_distance = 2.0

[node name="PerformAttack" type="Node" parent="CombatSelector/AttackSequence"]
script = ExtResource("8_combat")
target_var = &"combat_target"
default_attack = "light_upper"
wait_for_completion = true

[node name="FleeSequence" type="Node" parent="CombatSelector"]
script = ExtResource("3_combat")

[node name="FleeFromTarget" type="Node" parent="CombatSelector/FleeSequence"]
script = ExtResource("9_combat")
target_var = &"combat_target"
speed_multiplier = 1.5
safe_distance = 15.0

[node name="Wait" type="Node" parent="CombatSelector"]
script = ExtResource("5_combat")
wait_time = 0.5
