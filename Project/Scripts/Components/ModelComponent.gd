extends BaseComponent
class_name ModelComponent

signal animation_finished(anim_name)
signal animation_changed(anim_name)

@export var model_scene: PackedScene
@export var use_humanoid_model: bool = true
@export var use_split_body_animation: bool = false

var model_instance
var animator
var skeleton
var body_parts_blender
var split_body_animator

func _on_initialize():
	if model_scene:
		model_instance = model_scene.instantiate()
		add_child(model_instance)

	# Find animator and skeleton
	animator = _find_node_of_type(self, "AnimationPlayer")
	skeleton = _find_node_of_type(self, "Skeleton3D")
	body_parts_blender = _find_node_of_type(self, "AnimationTree")

	if animator:
		animator.connect("animation_finished", _on_animation_finished)

	# Set up split body animation if needed
	if use_split_body_animation and body_parts_blender:
		_setup_split_body_animation()

func play_animation(anim_name: String, blend: float = 0.2, custom_speed: float = 1.0, from_end: bool = false):
	if not animator:
		return

	if animator.has_animation(anim_name):
		animator.play(anim_name, blend, custom_speed, from_end)
		return true

	return false

func _on_animation_finished(anim_name: String):
	emit_signal("animation_finished", anim_name)

func _setup_split_body_animation():
	# Create body parts blender if it doesn't exist
	if not body_parts_blender:
		body_parts_blender = BodyPartsBlender.new()
		body_parts_blender.skeleton = skeleton
		body_parts_blender.animation_tree = _find_node_of_type(self, "AnimationTree")
		add_child(body_parts_blender)

	# Create split body animator if it doesn't exist
	if not split_body_animator:
		split_body_animator = SplitBodyAnimator.new()
		split_body_animator.animation_tree = _find_node_of_type(self, "AnimationTree")
		split_body_animator.model_component = self
		add_child(split_body_animator)

func _find_node_of_type(node: Node, type_name: String) -> Node:
	if node.get_class() == type_name:
		return node

	for child in node.get_children():
		var found = _find_node_of_type(child, type_name)
		if found:
			return found

	return null
