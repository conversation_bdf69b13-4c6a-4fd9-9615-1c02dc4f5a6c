extends BaseClass
class_name VitalClass

enum VitalType {WAR<PERSON><PERSON>, GUARDIAN, BERSERKER, PALADIN}

@export var vital_type: VitalType = VitalType.WARRIOR
@export var rage_multiplier: float = 1.5
@export var defense_boost: float = 0.3
@export var health_regen_rate: float = 0.05

var rage_mode: bool = false
var rage_timer: float = 0.0
var rage_duration: float = 10.0
var entity_ref: WeakRef

func _init():
    name = "Vital"
    class_description = "Masters of physical combat"
    
    # Vital class has higher health and attack but lower mana
    health_modifier = 1.4
    mana_modifier = 0.7
    stamina_modifier = 1.2
    attack_modifier = 1.2
    defense_modifier = 1.1
    speed_modifier = 0.9
    
    # Set up passive effects based on vital type
    _setup_vital_effects()

func _setup_vital_effects():
    match vital_type:
        VitalType.WARRIOR:
            passive_effects["critical_hit_chance"] = 0.15
            passive_effects["stamina_regen"] = 0.2
        VitalType.GUARDIAN:
            passive_effects["damage_reduction"] = 0.2
            passive_effects["taunt_strength"] = 0.3
        VitalType.BERSERKER:
            passive_effects["rage_duration_bonus"] = 0.5
            passive_effects["low_health_damage_bonus"] = 0.4
        VitalType.PALADIN:
            passive_effects["healing_bonus"] = 0.3
            passive_effects["holy_damage"] = 0.2

func apply_passive_effects(entity):
    # Store weak reference to entity
    entity_ref = weakref(entity)
    
    # Connect to process for health regeneration and rage mode
    entity.connect("process", _on_entity_process)
    
    # Connect to damage signals for rage mode
    var combat_component = entity.get_node_or_null("CombatComponent")
    if combat_component:
        combat_component.connect("damage_taken", _on_damage_taken)

func _on_entity_process(entity, delta):
    # Handle health regeneration
    var resource_component = entity.get_node_or_null("ResourceComponent")
    if resource_component:
        resource_component.health += resource_component.max_health * health_regen_rate * delta
    
    # Handle rage mode
    if rage_mode:
        rage_timer += delta
        if rage_timer >= rage_duration:
            _end_rage_mode(entity)

func _on_damage_taken(damage_amount, attacker):
    # Check if entity reference is still valid
    if !entity_ref.get_ref():
        return
        
    var entity = entity_ref.get_ref()
    
    # Check if we should activate rage mode
    var resource_component = entity.get_node_or_null("ResourceComponent")
    if resource_component and resource_component.health < resource_component.max_health * 0.3:
        activate_rage_mode(entity)

func activate_rage_mode(entity):
    if not rage_mode:
        rage_mode = true
        rage_timer = 0.0
        
        # Apply rage effects
        var combat_component = entity.get_node_or_null("CombatComponent")
        if combat_component:
            combat_component.base_damage *= rage_multiplier
        
        # Visual effects
        var model_component = entity.get_node_or_null("ModelComponent")
        if model_component and model_component.model_instance:
            # Add rage particle effect
            var rage_effect = load("res://Project/Effects/rage_effect.tscn")
            if rage_effect:
                var effect_instance = rage_effect.instantiate()
                model_component.add_child(effect_instance)
                effect_instance.name = "RageEffect"

func _end_rage_mode(entity):
    if rage_mode:
        rage_mode = false
        
        # Remove rage effects
        var combat_component = entity.get_node_or_null("CombatComponent")
        if combat_component:
            combat_component.base_damage /= rage_multiplier
        
        # Remove visual effects
        var model_component = entity.get_node_or_null("ModelComponent")
        if model_component:
            var rage_effect = model_component.get_node_or_null("RageEffect")
            if rage_effect:
                rage_effect.queue_free()